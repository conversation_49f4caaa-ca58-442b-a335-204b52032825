package com.eatapp.clementine.views

import android.content.Context
import android.graphics.Matrix
import android.graphics.PointF
import android.util.AttributeSet
import android.util.Log
import android.view.GestureDetector
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.get
import androidx.databinding.DataBindingUtil
import com.bumptech.glide.Glide
import com.eatapp.clementine.R
import com.eatapp.clementine.data.network.response.room.TableType
import com.eatapp.clementine.databinding.RoomViewBinding
import kotlin.math.max
import kotlin.math.min

// Extension function to set center position
fun View.setCenterPosition(centerX: Float, centerY: Float) {
    x = centerX - width / 2f
    y = centerY - height / 2f
}

class RoomView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private val roomImageFormat: String = "https://ucarecdn.com/%s/-/preview/800x800/"

    var listener: ((tableState: TableState) -> Unit)? = null

    private var binding: RoomViewBinding = DataBindingUtil.inflate(
        LayoutInflater.from(context),
        R.layout.room_view, this, true
    )

    // Zoom and pan functionality
    private var matrix = Matrix()
    private var savedMatrix = Matrix()
    private var scaleGestureDetector: ScaleGestureDetector
    private var gestureDetector: GestureDetector

    // Touch states
    private enum class Mode { NONE, DRAG, ZOOM }

    private var mode = Mode.NONE

    // Scale and position tracking
    private var scaleFactor = 1.0f
    private var minScale = 1f
    private var maxScale = 3.0f
    private var lastTouchX = 0f
    private var lastTouchY = 0f
    private var startPoint = PointF()

    // View dimensions for boundary checking
    private var viewWidth = 0
    private var viewHeight = 0

    init {
        // Initialize gesture detectors
        scaleGestureDetector = ScaleGestureDetector(context, ScaleListener())
        gestureDetector = GestureDetector(context, GestureListener()).apply {
            // Make double-tap detection less sensitive to avoid conflicts with panning
            setOnDoubleTapListener(GestureListener())
        }

        // Set initial matrix for the container and background
        binding.cont.pivotX = 0f
        binding.cont.pivotY = 0f
        binding.backgroundImage.pivotX = 0f
        binding.backgroundImage.pivotY = 0f
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        viewWidth = w
        viewHeight = h
    }

    override fun onInterceptTouchEvent(ev: MotionEvent): Boolean {
        // Always intercept multi-touch for zoom
        if (ev.pointerCount > 1) {
            return true
        }

        // For single touch, only intercept if we're zoomed in and it's a move event
        return when (ev.actionMasked) {
            MotionEvent.ACTION_DOWN -> {
                startPoint.set(ev.x, ev.y)
                false // Let child views handle single touch initially
            }

            MotionEvent.ACTION_MOVE -> {
                // Only intercept move events if we're zoomed in and moved enough
                if (scaleFactor > 1.0f) {
                    val dx = ev.x - startPoint.x
                    val dy = ev.y - startPoint.y
                    val distance = kotlin.math.sqrt(dx * dx + dy * dy)
                    distance > 10 // Only intercept if moved more than 10 pixels
                } else {
                    false
                }
            }

            else -> false
        }
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        var handled = false

        // Handle scale gestures first
        handled = scaleGestureDetector.onTouchEvent(event) || handled

        // Only handle other gestures if not scaling
        if (!scaleGestureDetector.isInProgress) {
            handled = gestureDetector.onTouchEvent(event) || handled
        }

        when (event.actionMasked) {
            MotionEvent.ACTION_DOWN -> {
                if (event.pointerCount == 1) {
                    savedMatrix.set(matrix)
                    lastTouchX = event.x
                    lastTouchY = event.y
                    mode = Mode.DRAG
                    handled = true
                }
            }

            MotionEvent.ACTION_MOVE -> {
                if (mode == Mode.DRAG && !scaleGestureDetector.isInProgress && event.pointerCount == 1) {
                    // Only allow dragging if zoomed in
                    if (scaleFactor > 1.0f) {
                        val dx = event.x - lastTouchX
                        val dy = event.y - lastTouchY

                        matrix.set(savedMatrix)
                        matrix.postTranslate(dx, dy)

                        // Apply boundary constraints
                        constrainMatrix()
                        applyMatrix()
                        handled = true
                    }
                }
            }

            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                mode = Mode.NONE
            }

            MotionEvent.ACTION_POINTER_UP -> {
                mode = Mode.NONE
            }
        }

        return handled
    }

    fun loadFloor(states: List<TableState>, backgroundImageId: String?) {
        // Reset zoom state when loading a new floor to ensure independent zoom per tab
        resetZoom()

        removeAllTables()

        states.forEach {
            addTable(it)
        }
        loadBackgroundImage(backgroundImageId)
    }

    fun updateFloor() {
        for (i in 0 until binding.cont.childCount) {
            val tableView: TableView? = binding.cont[i] as? TableView

            tableView?.tableState?.let {
                if (it.shouldUpdateView) {
                    binding.cont.removeView(tableView)
                    addTable(it, i)
                }
            }
        }
    }

    private fun removeAllTables() {
        binding.cont.removeAllViews()
    }

    private fun addTable(tableState: TableState, index: Int = 0) {

        val roomPadding = resources.getDimension(R.dimen.room_view_adjustment_padding) * 2

        val roomWidth: Float = measuredWidth - roomPadding
        val roomHeight = measuredHeight - roomPadding

        val tableWidth = (roomWidth / 75) * tableState.table.width
        val tableHeight = (roomWidth / 75) * tableState.table.height

        val tableView = TableView(context!!)
        tableView.initWithTable(tableState, tableWidth.toInt(), tableHeight.toInt())

        tableView.layoutParams = ViewGroup.LayoutParams(
            LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT
        )

        // Force measure and layout to get actual dimensions after rotation
        tableView.measure(
            MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED),
            MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED)
        )
        tableView.layout(0, 0, tableView.measuredWidth, tableView.measuredHeight)

        val shape = tableView.binding.mainCont
        val shapeCenterX = shape.x + shape.width / 2
        val shapeCenterY = shape.y + shape.height / 2

        tableView.x = (roomWidth * tableState.table.x).toFloat() - shapeCenterX + roomPadding / 2
        tableView.y = (roomHeight * tableState.table.y).toFloat() - shapeCenterY + roomPadding / 2

        Log.d(
            "tableview", "Table ${tableState.table.number}: x=${tableView.x}," +
                    " y=${tableView.y}, w=${tableView.width}, h=${tableView.height}"
        )

        tableView.listener = { state ->
            listener?.invoke(state)
        }

        tableView.z = if (tableState.table.type == TableType.Table) 99F else 88F

        binding.cont.addView(tableView, index)
    }

    private fun loadBackgroundImage(imageId: String?) {

        if (imageId?.isNotEmpty() == true) {
            val imageUrl = String.format(roomImageFormat, imageId)
            Glide.with(this)
                .load(imageUrl)
                .into(binding.backgroundImage)
        } else {
            binding.backgroundImage.setImageDrawable(null)
        }
    }

    private inner class ScaleListener : ScaleGestureDetector.SimpleOnScaleGestureListener() {
        override fun onScaleBegin(detector: ScaleGestureDetector): Boolean {
            mode = Mode.ZOOM
            return true
        }

        override fun onScale(detector: ScaleGestureDetector): Boolean {
            val scale = detector.scaleFactor
            val newScale = scaleFactor * scale

            // Constrain scale within limits
            val constrainedScale = max(minScale, min(newScale, maxScale))
            val actualScale = constrainedScale / scaleFactor

            if (actualScale != 1.0f) {
                scaleFactor = constrainedScale

                // Scale around the focal point
                matrix.postScale(actualScale, actualScale, detector.focusX, detector.focusY)

                // Apply boundary constraints
                constrainMatrix()
                applyMatrix()
            }

            return true
        }

        override fun onScaleEnd(detector: ScaleGestureDetector) {
            mode = Mode.NONE
        }
    }

    private inner class GestureListener : GestureDetector.SimpleOnGestureListener() {
        override fun onDoubleTap(e: MotionEvent): Boolean {
            // Only handle double tap if we're not in the middle of a drag
            if (mode != Mode.DRAG) {
                val targetScale = if (scaleFactor > 1.5f) 1.0f else 2.0f
                animateScaleTo(targetScale, e.x, e.y)
                return true
            }
            return false
        }

        override fun onSingleTapConfirmed(e: MotionEvent): Boolean {
            // Only consume single tap if zoomed in (to prevent table clicks when zoomed)
            return false // Let table clicks work normally
        }

        override fun onDown(e: MotionEvent): Boolean {
            // Always return true to indicate we want to handle the gesture sequence
            return true
        }
    }

    private fun animateScaleTo(targetScale: Float, focusX: Float, focusY: Float) {
        // Simple scale animation - you could use ValueAnimator for smoother animation
        val scaleChange = targetScale / scaleFactor
        scaleFactor = targetScale

        matrix.postScale(scaleChange, scaleChange, focusX, focusY)

        // Reset position if zooming out to 1.0
        if (targetScale == 1.0f) {
            matrix.reset()
            scaleFactor = 1.0f
        } else {
            constrainMatrix()
        }

        applyMatrix()
    }

    private fun constrainMatrix() {
        val values = FloatArray(9)
        matrix.getValues(values)

        val transX = values[Matrix.MTRANS_X]
        val transY = values[Matrix.MTRANS_Y]
        val scaleX = values[Matrix.MSCALE_X]
        val scaleY = values[Matrix.MSCALE_Y]

        // Get the actual content size (container size)
        val contentWidth = binding.cont.width.toFloat()
        val contentHeight = binding.cont.height.toFloat()

        val scaledWidth = contentWidth * scaleX
        val scaledHeight = contentHeight * scaleY

        // When zoomed in, allow much more generous boundaries to use full screen space
        if (scaleX > 1.0f) {
            // Allow panning to use the full parent view space
            val parentView = parent as? ViewGroup
            val availableWidth = parentView?.width?.toFloat() ?: viewWidth.toFloat()
            val availableHeight = parentView?.height?.toFloat() ?: viewHeight.toFloat()

            // Calculate how much we can pan to show more content
            val maxTransX = (availableWidth - viewWidth) / 2f + (scaledWidth - viewWidth) / 2f
            val minTransX = -(availableWidth - viewWidth) / 2f - (scaledWidth - viewWidth) / 2f
            val maxTransY = (availableHeight - viewHeight) / 2f + (scaledHeight - viewHeight) / 2f
            val minTransY = -(availableHeight - viewHeight) / 2f - (scaledHeight - viewHeight) / 2f

            // Apply generous constraints
            val constrainedTransX = max(minTransX, min(transX, maxTransX))
            val constrainedTransY = max(minTransY, min(transY, maxTransY))

            values[Matrix.MTRANS_X] = constrainedTransX
            values[Matrix.MTRANS_Y] = constrainedTransY
        } else {
            // Normal zoom level - center the content
            val centerX = (viewWidth - scaledWidth) / 2f
            val centerY = (viewHeight - scaledHeight) / 2f

            values[Matrix.MTRANS_X] = centerX
            values[Matrix.MTRANS_Y] = centerY
        }

        matrix.setValues(values)
    }

    private fun applyMatrix() {
        val values = FloatArray(9)
        matrix.getValues(values)

        val transX = values[Matrix.MTRANS_X]
        val transY = values[Matrix.MTRANS_Y]
        val scaleX = values[Matrix.MSCALE_X]
        val scaleY = values[Matrix.MSCALE_Y]

        // When zoomed in, allow the view to expand beyond its original bounds
        if (scaleX > 1.0f) {
            // Temporarily expand the view's clipping to allow content to show beyond bounds
            clipChildren = false
            clipToPadding = false

            // Also ensure parent doesn't clip
            (parent as? ViewGroup)?.let { parentView ->
                parentView.clipChildren = false
                parentView.clipToPadding = false
            }
        } else {
            // Restore normal clipping when not zoomed
            clipChildren = true
            clipToPadding = true

            (parent as? ViewGroup)?.let { parentView ->
                parentView.clipChildren = true
                parentView.clipToPadding = true
            }
        }

        // Apply transformations to both the container and background image
        // to keep them perfectly synchronized
        binding.cont.apply {
            translationX = transX
            translationY = transY
            this.scaleX = scaleX
            this.scaleY = scaleY
            pivotX = 0f
            pivotY = 0f
        }

        binding.backgroundImage.apply {
            translationX = transX
            translationY = transY
            this.scaleX = scaleX
            this.scaleY = scaleY
            pivotX = 0f
            pivotY = 0f
        }
    }

    // Public method to reset zoom
    fun resetZoom() {
        matrix.reset()
        scaleFactor = 1.0f
        mode = Mode.NONE

        // Restore normal clipping
        clipChildren = true
        clipToPadding = true
        (parent as? ViewGroup)?.let { parentView ->
            parentView.clipChildren = true
            parentView.clipToPadding = true
        }

        // Reset both container and background image transformations
        binding.cont.apply {
            translationX = 0f
            translationY = 0f
            this.scaleX = 1.0f
            this.scaleY = 1.0f
        }

        binding.backgroundImage.apply {
            translationX = 0f
            translationY = 0f
            this.scaleX = 1.0f
            this.scaleY = 1.0f
        }
    }

    // Public method to check if zoomed
    fun isZoomed(): Boolean = scaleFactor > 1.0f

    // Call this method when switching tabs to ensure independent zoom states
    fun resetZoomForTabSwitch() {
        if (isZoomed()) {
            resetZoom()
        }
    }
}